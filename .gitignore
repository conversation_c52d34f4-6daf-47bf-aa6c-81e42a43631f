<<<<<<< HEAD
# Python
=======
# React Dependencies
node_modules/

# React Build outputs
.next/
.next/cache/
.next/static/
.next/standalone/
.next/server/
.next/BUILD_ID  
.next/server

# React Build artifacts

build/
dist/

# React Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# React Testing and Coverage
coverage/
.nyc_output/

# React Cache directories
.cache/
.parcel-cache/

# React Package manager files
package-lock.json
yarn.lock
.pnp
.pnp.js

# React ESLint cache
.eslintcache

# Flask Python files
>>>>>>> origin/main
__pycache__/
*.py[cod]
*$py.class
*.so
<<<<<<< HEAD
=======

# Flask Distribution / packaging
>>>>>>> origin/main
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
<<<<<<< HEAD
=======
pip-wheel-metadata/
share/python-wheels/
>>>>>>> origin/main
*.egg-info/
.installed.cfg
*.egg
MANIFEST

<<<<<<< HEAD
# Virtual environments
=======
# Flask Virtual environments
>>>>>>> origin/main
venv/
env/
ENV/
env.bak/
venv.bak/
<<<<<<< HEAD

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
=======
.venv/

# Flask Instance folder
instance/

# Flask Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# Flask Database
*.db
*.sqlite
*.sqlite3

# Flask Logs
*.log
logs/

# IDE and Editor files
>>>>>>> origin/main
.vscode/
.idea/
*.swp
*.swo
*~

<<<<<<< HEAD
# OS
=======
# OS generated files
>>>>>>> origin/main
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

<<<<<<< HEAD
# Logs
*.log
logs/

# Database
*.db
*.sqlite
*.sqlite3

# Node modules (for frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js
.next/
out/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Backup files
*.bak
*.backup
*.tmp
=======
# Temporary files
tmp/
temp/
*.tmp
*.temp

# Archive files
*.zip
*.tar.gz
*.rar

>>>>>>> origin/main
