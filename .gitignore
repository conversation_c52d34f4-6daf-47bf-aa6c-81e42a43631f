# React Dependencies
node_modules/

# React Build outputs
frontend/.next/
frontend/.next/cache/
frontend/.next/static/
frontend/.next/standalone/
frontend/.next/server/
frontend/.next/BUILD_ID
out/
build/
dist/

# React Environment Variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# React Testing and Coverage
coverage/
.nyc_output/
.pytest_cache/
.coverage
.coverage.*
coverage.xml

# React Cache
.cache/
.parcel-cache/

# React Package manager files
package-lock.json
yarn.lock
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnp
.pnp.js

# React ESLint cache
.eslintcache

# Flask Python Files
__pycache__/
*.py[cod]
*$py.class
*.so

# Flask Packaging and Distribution
.Python
develop-eggs/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Flask Virtual Environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# Flask Instance Folder
instance/

# Flask Logs
*.log
logs/

# Flask Databases
*.db
*.sqlite
*.sqlite3

# Flask Test Coverage
htmlcov/
.tox/
.nox/

# Jupyter
.ipynb_checkpoints

# pyenv
.python-version

# IDEs and Editor Files
.vscode/
.idea/
*.swp
*.swo
*~

# OS Generated Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup and Temp Files
*.bak
*.backup
*.tmp
tmp/
temp/